<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Редактирование моба - Админка</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Стили для улучшенного селектора локаций */
        .location-selector {
            position: relative;
        }

        .location-search {
            width: 100%;
            background: #1e1d1b;
            color: #f5f5f5;
            border: 1px solid #a6925e;
            border-radius: 8px;
            padding: 12px;
        }

        .location-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #2a2824;
            border: 1px solid #a6925e;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .location-group-header {
            background: #3b3a33;
            color: #e5b769;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 14px;
        }

        .location-option {
            padding: 8px 12px;
            cursor: pointer;
            color: #d9d3b8;
            border-bottom: 1px solid #3b3a33;
        }

        .location-option:hover {
            background: #3b3a33;
            color: #e5b769;
        }

        .location-option.selected {
            background: #a6925e;
            color: #2f2d2b;
        }

        .no-results {
            padding: 12px;
            text-align: center;
            color: #a6925e;
            font-style: italic;
        }
    </style>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    <div class="min-h-screen flex flex-col">
        
        <main class="flex-grow p-2 md:p-4">
            <div
                class="container max-w-6xl mx-auto bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg">
                
                <div class="flex items-center justify-between px-4 py-3 border-b border-[#a6925e]">
                    
                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                        class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300 shadow-md">
                        🏠
                    </a>

                    
                    <div class="flex items-center text-sm text-[#d9d3b8]">
                        <a href="<?php echo e(route('admin.dashboard')); ?>"
                            class="text-[#a6925e] hover:text-[#e5b769]">Админ-панель</a>
                        <span class="mx-2">›</span>
                        <a href="<?php echo e(route('admin.mobs.index')); ?>" class="text-[#a6925e] hover:text-[#e5b769]">Мобы</a>
                        <span class="mx-2">›</span>
                        <span>Редактирование: <?php echo e($mob->name); ?></span>
                    </div>

                    <a href="<?php echo e(route('admin.mobs.index')); ?>"
                        class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-2 px-4 rounded-lg transition duration-300 shadow-md">
                        ← Назад к списку
                    </a>
                </div>

                
                <div class="px-6 py-4 text-center border-b border-[#a6925e]">
                    <h1 class="text-3xl font-bold text-[#e5b769]">Редактирование моба #<?php echo e($mob->id); ?></h1>
                    <p class="text-[#d9d3b8] mt-2"><?php echo e($mob->name); ?></p>
                </div>

                
                <?php if($errors->any()): ?>
                    <div class="bg-[#a65e5e] text-white p-4 m-4 rounded">
                        <ul class="list-disc pl-5">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                
                <form action="<?php echo e(route('admin.mobs.update', $mob->id)); ?>" method="POST" class="p-4">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        
                        <div class="space-y-6">
                            
                            <div class="bg-[#3b3a33] p-4 rounded-lg border border-[#a6925e]">
                                <h2 class="text-xl font-semibold text-[#e5b769] mb-4">Основная информация</h2>

                                
                                <div class="mb-4">
                                    <label for="name" class="block text-[#d9d3b8] font-semibold mb-1">Название
                                        моба:</label>
                                    <input type="text" name="name" id="name" value="<?php echo e(old('name', $mob->name)); ?>"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="location_search"
                                        class="block text-[#d9d3b8] font-semibold mb-1">Локация:</label>
                                    <div class="location-selector">
                                        <input type="text" id="location_search"
                                            class="location-search focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                            placeholder="Поиск локации..." autocomplete="off">
                                        <input type="hidden" name="location_id" id="location_id"
                                            value="<?php echo e(old('location_id', $mob->location_id)); ?>">

                                        <div class="location-dropdown" id="location_dropdown">
                                            
                                        </div>
                                    </div>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="level" class="block text-[#d9d3b8] font-semibold mb-1">Уровень:</label>
                                    <input type="number" name="level" id="level" value="<?php echo e(old('level', $mob->level)); ?>"
                                        min="1"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div>
                                    <label for="description"
                                        class="block text-[#d9d3b8] font-semibold mb-1">Описание:</label>
                                    <textarea name="description" id="description" rows="4"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"><?php echo e(old('description', $mob->description)); ?></textarea>
                                </div>
                            </div>

                            
                            <div class="bg-[#3b3a33] p-4 rounded-lg border border-[#a6925e]">
                                <h2 class="text-xl font-semibold text-[#e5b769] mb-4">Награды</h2>

                                
                                <div class="mb-4">
                                    <label for="experience_reward" class="block text-[#d9d3b8] font-semibold mb-1">Опыт
                                        за убийство:</label>
                                    <input type="number" name="experience_reward" id="experience_reward"
                                        value="<?php echo e(old('experience_reward', $mob->experience_reward)); ?>" min="0"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div>
                                    <label for="item_drop_chance" class="block text-[#d9d3b8] font-semibold mb-1">Шанс
                                        дропа предмета (%):</label>
                                    <input type="number" name="item_drop_chance" id="item_drop_chance"
                                        value="<?php echo e(old('item_drop_chance', $mob->item_drop_chance)); ?>" min="0" max="100"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]">
                                </div>
                            </div>
                        </div>

                        
                        <div class="space-y-6">
                            
                            <div class="bg-[#3b3a33] p-4 rounded-lg border border-[#a6925e]">
                                <h2 class="text-xl font-semibold text-[#e5b769] mb-4">Характеристики</h2>

                                
                                <div class="mb-4">
                                    <label for="max_hp" class="block text-[#d9d3b8] font-semibold mb-1">Максимальное
                                        здоровье:</label>
                                    <input type="number" name="max_hp" id="max_hp"
                                        value="<?php echo e(old('max_hp', $mob->max_hp)); ?>" min="1"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="strength" class="block text-[#d9d3b8] font-semibold mb-1">Сила:</label>
                                    <input type="number" name="strength" id="strength"
                                        value="<?php echo e(old('strength', $mob->strength)); ?>" min="1"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="defense" class="block text-[#d9d3b8] font-semibold mb-1">Защита:</label>
                                    <input type="number" name="defense" id="defense"
                                        value="<?php echo e(old('defense', $mob->defense)); ?>" min="0"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="agility"
                                        class="block text-[#d9d3b8] font-semibold mb-1">Ловкость:</label>
                                    <input type="number" name="agility" id="agility"
                                        value="<?php echo e(old('agility', $mob->agility)); ?>" min="0"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="intelligence"
                                        class="block text-[#d9d3b8] font-semibold mb-1">Интеллект:</label>
                                    <input type="number" name="intelligence" id="intelligence"
                                        value="<?php echo e(old('intelligence', $mob->intelligence)); ?>" min="0"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="crit_chance" class="block text-[#d9d3b8] font-semibold mb-1">Шанс
                                        критического удара (%):</label>
                                    <input type="number" name="crit_chance" id="crit_chance"
                                        value="<?php echo e(old('crit_chance', $mob->crit_chance)); ?>" min="0" max="100"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div>
                                    <label for="crit_multiplier"
                                        class="block text-[#d9d3b8] font-semibold mb-1">Множитель крита (x):</label>
                                    <input type="number" name="crit_multiplier" id="crit_multiplier"
                                        value="<?php echo e(old('crit_multiplier', $mob->crit_multiplier)); ?>" min="1" step="0.1"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                </div>

                                
                                <div class="mb-4">
                                    <label for="respawn_time" class="block text-[#d9d3b8] font-semibold mb-1">Время
                                        респауна (мин):</label>
                                    <input type="number" name="respawn_time" id="respawn_time"
                                        value="<?php echo e(old('respawn_time', $mob->respawn_time ?? 3)); ?>" min="1"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]"
                                        required>
                                    <p class="text-[#a6925e] text-xs italic mt-1">Время в минутах до автоматического
                                        респауна моба после смерти.</p>
                                </div>
                            </div>

                            
                            <div class="bg-[#3b3a33] p-4 rounded-lg border border-[#a6925e]">
                                <h2 class="text-xl font-semibold text-[#e5b769] mb-4">Дополнительно</h2>

                                
                                <div class="mb-4">
                                    <label for="icon" class="block text-[#d9d3b8] font-semibold mb-1">URL
                                        иконки:</label>
                                    <input type="text" name="icon" id="icon" value="<?php echo e(old('icon', $mob->icon)); ?>"
                                        class="w-full bg-[#2a2824] text-[#f5f5f5] border border-[#a6925e] rounded p-2 focus:outline-none focus:border-[#e5b769]">
                                </div>

                                
                                <div class="mb-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="is_active" id="is_active" value="1" <?php echo e(old('is_active', $mob->is_active) ? 'checked' : ''); ?>

                                            class="form-checkbox h-5 w-5 text-[#a6925e] rounded focus:ring-0 focus:ring-offset-0">
                                        <span class="ml-2 text-[#d9d3b8] font-semibold">Активный (доступен в
                                            игре)</span>
                                    </label>
                                </div>

                                
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="is_boss" id="is_boss" value="1" <?php echo e(old('is_boss', $mob->is_boss) ? 'checked' : ''); ?>

                                            class="form-checkbox h-5 w-5 text-[#a6925e] rounded focus:ring-0 focus:ring-offset-0">
                                        <span class="ml-2 text-[#d9d3b8] font-semibold">Босс</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    
                    <div class="flex justify-between mt-6">
                        <a href="<?php echo e(route('admin.mobs.index')); ?>"
                            class="bg-[#5e5e5e] hover:bg-[#707070] text-white py-2 px-6 rounded">
                            Отмена
                        </a>
                        <button type="submit"
                            class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-2 px-6 rounded font-semibold">
                            Сохранить изменения
                        </button>
                    </div>
                </form>

                
                <footer class="bg-[#2a2721] text-[#d9d3b8] py-4 border-t border-[#a6925e]">
                    <div class="text-center">
                        <p class="text-sm">
                            <a href="<?php echo e(route('admin.dashboard')); ?>"
                                class="text-[#a6925e] hover:text-[#e5b769] transition duration-300">
                                ← Вернуться в админ-панель
                            </a>
                        </p>
                    </div>
                </footer>
            </div>
        </main>
    </div>

    
    <script>
        $(document).ready(function () {
            // Данные локаций из PHP - передаем как простой массив
            const allLocations = [
                <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    {
                        id: "<?php echo e($location->id); ?>",
                        name: "<?php echo e(addslashes($location->name)); ?>",
                        type: "<?php echo e($location->location_type); ?>"
                    },
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            ];

            // Группируем локации по типам
            const locations = {};
            allLocations.forEach(location => {
                if (!locations[location.type]) {
                    locations[location.type] = [];
                }
                locations[location.type].push(location);
            });

            // Переводы типов локаций
            const locationTypeNames = {
                'main': 'Основные локации',
                'outpost': 'Аванпосты',
                'mine': 'Рудники',
                'mine_area': 'Карьеры',
                'forest': 'Леса',
                'canyon': 'Каньоны',
                'cave': 'Пещеры',
                'dungeon': 'Подземелья'
            };

            // Определяем выбранную локацию или подземелье
            let selectedLocationId = '';
            let selectedLocationName = '';

            <?php if(old('location_id')): ?>
                selectedLocationId = '<?php echo e(old('location_id')); ?>';
            <?php elseif($mob->location_id): ?>
                selectedLocationId = '<?php echo e($mob->location_id); ?>';
            <?php elseif(str_starts_with($mob->location, 'Подземелье:')): ?>
                // Для мобов из подземелий ищем соответствующее подземелье
                <?php
                    $dungeonName = str_replace('Подземелье: ', '', $mob->location);
                    $dungeon = \App\Models\Dungeon::where('name', $dungeonName)->first();
                ?>
                    <?php if($dungeon): ?>
                        selectedLocationId = 'dungeon_<?php echo e($dungeon->id); ?>';
                    <?php endif; ?>
            <?php endif; ?>

            // Найти название выбранной локации
            if (selectedLocationId) {
                Object.keys(locations).forEach(type => {
                    locations[type].forEach(location => {
                        if (location.id == selectedLocationId) {
                            selectedLocationName = location.name;
                        }
                    });
                });
                $('#location_search').val(selectedLocationName);
            }

            // Функция для отображения выпадающего списка
            function showDropdown(searchTerm = '') {
                const dropdown = $('#location_dropdown');
                dropdown.empty();

                let hasResults = false;
                const searchLower = searchTerm.toLowerCase();

                Object.keys(locations).forEach(type => {
                    const typeLocations = locations[type].filter(location =>
                        location.name.toLowerCase().includes(searchLower)
                    );

                    if (typeLocations.length > 0) {
                        hasResults = true;

                        // Заголовок группы
                        const groupHeader = $('<div class="location-group-header"></div>')
                            .text(locationTypeNames[type] || type);
                        dropdown.append(groupHeader);

                        // Локации в группе
                        typeLocations.forEach(location => {
                            const option = $('<div class="location-option"></div>')
                                .text(location.name)
                                .data('id', location.id)
                                .data('name', location.name);

                            if (location.id == selectedLocationId) {
                                option.addClass('selected');
                            }

                            option.click(function () {
                                selectedLocationId = $(this).data('id');
                                selectedLocationName = $(this).data('name');
                                $('#location_id').val(selectedLocationId);
                                $('#location_search').val(selectedLocationName);
                                dropdown.hide();
                            });

                            dropdown.append(option);
                        });
                    }
                });

                if (!hasResults) {
                    dropdown.append('<div class="no-results">Локации не найдены</div>');
                }

                dropdown.show();
            }

            // Обработчики событий
            $('#location_search').on('focus', function () {
                showDropdown($(this).val());
            });

            $('#location_search').on('input', function () {
                showDropdown($(this).val());
            });

            // Скрыть выпадающий список при клике вне его
            $(document).on('click', function (e) {
                if (!$(e.target).closest('.location-selector').length) {
                    $('#location_dropdown').hide();
                }
            });

            // Очистка поля при изменении текста
            $('#location_search').on('input', function () {
                const currentValue = $(this).val();
                if (currentValue !== selectedLocationName) {
                    selectedLocationId = '';
                    selectedLocationName = '';
                    $('#location_id').val('');
                }
            });
        });
    </script>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/admin/mobs/edit.blade.php ENDPATH**/ ?>