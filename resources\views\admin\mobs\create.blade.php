<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Создание моба - Админка</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Стили для улучшенного селектора локаций */
        .location-selector {
            position: relative;
        }

        .location-search {
            width: 100%;
            background: #2a2824;
            color: #f5f5f5;
            border: 1px solid #a6925e;
            border-radius: 4px;
            padding: 8px;
        }

        .location-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #2a2824;
            border: 1px solid #a6925e;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .location-group {
            border-bottom: 1px solid #3b3a33;
        }

        .location-group-header {
            background: #3b3a33;
            color: #e5b769;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 14px;
        }

        .location-option {
            padding: 8px 12px;
            cursor: pointer;
            color: #d9d3b8;
            border-bottom: 1px solid #3b3a33;
        }

        .location-option:hover {
            background: #3b3a33;
            color: #e5b769;
        }

        .location-option.selected {
            background: #a6925e;
            color: #2f2d2b;
        }

        .no-results {
            padding: 12px;
            text-align: center;
            color: #a6925e;
            font-style: italic;
        }
    </style>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    {{-- Основной контейнер --}}
    <div class="min-h-screen flex flex-col">
        {{-- Основное содержимое --}}
        <main class="flex-grow p-2 md:p-4">
            <div
                class="container max-w-6xl mx-auto bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg">
                {{-- Верхняя панель --}}
                <div class="flex items-center justify-between px-4 py-3 border-b border-[#a6925e]">
                    {{-- Кнопка "Домик" --}}
                    <a href="{{ route('admin.dashboard') }}"
                        class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300 shadow-md">
                        🏠
                    </a>

                    {{-- Хлебные крошки --}}
                    <div class="flex items-center text-sm text-[#d9d3b8]">
                        <a href="{{ route('admin.dashboard') }}"
                            class="text-[#a6925e] hover:text-[#e5b769]">Админ-панель</a>
                        <span class="mx-2">›</span>
                        <a href="{{ route('admin.mobs.index') }}" class="text-[#a6925e] hover:text-[#e5b769]">Мобы</a>
                        <span class="mx-2">›</span>
                        <span>Создание моба</span>
                    </div>

                    {{-- Кнопка "Обновить" --}}
                    <button onclick="location.reload()"
                        class="w-10 h-10 bg-[#a6925e] flex items-center justify-center rounded-lg hover:bg-[#e5b769] transition duration-300 shadow-md">
                        𖦹
                    </button>
                </div>

                {{-- Заголовок --}}
                <div class="px-6 py-4 text-center border-b border-[#a6925e]">
                    <h1 class="text-3xl font-bold text-[#e5b769]">Создание нового моба</h1>
                    <p class="text-[#d9d3b8] mt-2">Заполните форму для добавления нового моба в игру</p>
                </div>

                {{-- Сообщения об ошибках --}}
                @if ($errors->any())
                    <div class="mx-6 mt-4 bg-red-600 border border-red-700 text-white p-4 rounded-lg">
                        <h4 class="font-bold mb-2">Обнаружены ошибки:</h4>
                        <ul class="list-disc pl-5 space-y-1">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                {{-- Форма создания моба --}}
                <div class="px-6 py-6">
                    <form action="{{ route('admin.mobs.store') }}" method="POST" class="space-y-8">
                        @csrf

                        {{-- Основные данные --}}
                        <div class="bg-[#2a2721] border border-[#a6925e] rounded-lg p-6 shadow-md">
                            <h2 class="text-2xl font-bold text-[#e5b769] mb-6 flex items-center">
                                <span class="mr-3">📋</span>
                                Основная информация
                            </h2>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {{-- Название --}}
                                <div class="space-y-2">
                                    <label for="name" class="block text-[#d9d3b8] font-semibold text-sm">Название
                                        моба:</label>
                                    <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="Введите название моба">
                                    @error('name')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Улучшенный селектор локации --}}
                                <div class="space-y-2">
                                    <label for="location_search"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Локация:</label>
                                    <div class="location-selector">
                                        <input type="text" id="location_search"
                                            class="location-search focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                            placeholder="Поиск локации..." autocomplete="off">
                                        <input type="hidden" name="location_id" id="location_id"
                                            value="{{ old('location_id') }}">

                                        <div class="location-dropdown" id="location_dropdown">
                                            {{-- Контент будет заполнен через JavaScript --}}
                                        </div>
                                    </div>
                                    @error('location_id')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Уровень --}}
                                <div class="space-y-2">
                                    <label for="level"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Уровень:</label>
                                    <input type="number" name="level" id="level" value="{{ old('level', 1) }}" min="1"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="1">
                                    @error('level')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Опыт за победу --}}
                                <div class="space-y-2">
                                    <label for="experience_reward"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Опыт за победу:</label>
                                    <input type="number" name="experience_reward" id="experience_reward"
                                        value="{{ old('experience_reward', 10) }}" min="0"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="10">
                                    @error('experience_reward')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Награда золотом --}}
                                <div class="space-y-2">
                                    <label for="gold_reward" class="block text-[#d9d3b8] font-semibold text-sm">Золото
                                        за победу:</label>
                                    <input type="number" name="gold_reward" id="gold_reward"
                                        value="{{ old('gold_reward', 5) }}" min="0"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="5">
                                    @error('gold_reward')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            {{-- Описание --}}
                            <div class="mt-6 space-y-2">
                                <label for="description"
                                    class="block text-[#d9d3b8] font-semibold text-sm">Описание:</label>
                                <textarea name="description" id="description" rows="4"
                                    class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300 resize-vertical"
                                    placeholder="Введите описание моба...">{{ old('description') }}</textarea>
                                @error('description')
                                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        {{-- Характеристики --}}
                        <div class="bg-[#2a2721] border border-[#a6925e] rounded-lg p-6 shadow-md">
                            <h2 class="text-2xl font-bold text-[#e5b769] mb-6 flex items-center">
                                <span class="mr-3">⚔️</span>
                                Характеристики
                            </h2>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {{-- Здоровье --}}
                                <div class="space-y-2">
                                    <label for="max_hp"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Здоровье:</label>
                                    <input type="number" name="max_hp" id="max_hp" value="{{ old('max_hp', 100) }}"
                                        min="1"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="100">
                                    @error('max_hp')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Сила --}}
                                <div class="space-y-2">
                                    <label for="strength"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Сила:</label>
                                    <input type="number" name="strength" id="strength" value="{{ old('strength', 10) }}"
                                        min="1"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="10">
                                    @error('strength')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Защита --}}
                                <div class="space-y-2">
                                    <label for="defense"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Защита:</label>
                                    <input type="number" name="defense" id="defense" value="{{ old('defense', 5) }}"
                                        min="0"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="5">
                                    @error('defense')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Ловкость --}}
                                <div class="space-y-2">
                                    <label for="agility"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Ловкость:</label>
                                    <input type="number" name="agility" id="agility" value="{{ old('agility', 8) }}"
                                        min="1"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="8">
                                    @error('agility')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Интеллект --}}
                                <div class="space-y-2">
                                    <label for="intelligence"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Интеллект:</label>
                                    <input type="number" name="intelligence" id="intelligence"
                                        value="{{ old('intelligence', 5) }}" min="1"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="5">
                                    @error('intelligence')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Шанс крита --}}
                                <div class="space-y-2">
                                    <label for="crit_chance" class="block text-[#d9d3b8] font-semibold text-sm">Шанс
                                        крит. удара (%):</label>
                                    <input type="number" name="crit_chance" id="crit_chance"
                                        value="{{ old('crit_chance', 5) }}" min="0" max="100"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="5">
                                    @error('crit_chance')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Множитель крита --}}
                                <div class="space-y-2">
                                    <label for="crit_multiplier"
                                        class="block text-[#d9d3b8] font-semibold text-sm">Множитель крит.
                                        удара:</label>
                                    <input type="number" name="crit_multiplier" id="crit_multiplier"
                                        value="{{ old('crit_multiplier', 1.5) }}" min="1" step="0.1"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="1.5">
                                    @error('crit_multiplier')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                {{-- Время респауна --}}
                                <div class="space-y-2">
                                    <label for="respawn_time" class="block text-[#d9d3b8] font-semibold text-sm">Время
                                        респауна (мин):</label>
                                    <input type="number" name="respawn_time" id="respawn_time"
                                        value="{{ old('respawn_time', 3) }}" min="1"
                                        class="w-full bg-[#1e1d1b] text-[#f5f5f5] border border-[#a6925e] rounded-lg p-3 focus:outline-none focus:border-[#e5b769] focus:ring-2 focus:ring-[#e5b769] focus:ring-opacity-20 transition duration-300"
                                        placeholder="3">
                                    <p class="text-[#a6925e] text-xs italic mt-1">Время в минутах до автоматического
                                        респауна моба после смерти.</p>
                                    @error('respawn_time')
                                        <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        {{-- Кнопки действий --}}
                        <div class="flex flex-wrap justify-center gap-6 pt-6 border-t border-[#a6925e]">
                            <a href="{{ route('admin.mobs.index') }}"
                                class="bg-[#5e5e5e] hover:bg-[#707070] text-white py-3 px-8 rounded-lg font-semibold transition duration-300 shadow-md">
                                ← Отмена
                            </a>
                            <button type="submit"
                                class="bg-[#5e7ba6] hover:bg-[#718ebd] text-white py-3 px-8 rounded-lg font-semibold transition duration-300 shadow-md">
                                ✓ Создать моба
                            </button>
                        </div>
                    </form>
                </div>

                {{-- Футер --}}
                <footer class="bg-[#2a2721] text-[#d9d3b8] py-4 border-t border-[#a6925e]">
                    <div class="text-center">
                        <p class="text-sm">
                            <a href="{{ route('admin.dashboard') }}"
                                class="text-[#a6925e] hover:text-[#e5b769] transition duration-300">
                                ← Вернуться в админ-панель
                            </a>
                        </p>
                    </div>
                </footer>
            </div>
        </main>
    </div>

    {{-- JavaScript для улучшенного селектора локаций --}}
    <script>
        $(document).ready(function () {
            // Данные локаций из PHP - передаем как простой массив
            const allLocations = [
                @foreach($locations as $location)
                        {
                        id: "{{ $location->id }}",
                        name: "{{ addslashes($location->name) }}",
                        type: "{{ $location->location_type }}"
                    },
                @endforeach
            ];

            // Группируем локации по типам
            const locations = {};
            allLocations.forEach(location => {
                if (!locations[location.type]) {
                    locations[location.type] = [];
                }
                locations[location.type].push(location);
            });

            // Переводы типов локаций
            const locationTypeNames = {
                'main': 'Основные локации',
                'outpost': 'Аванпосты',
                'mine': 'Рудники',
                'mine_area': 'Карьеры',
                'forest': 'Леса',
                'canyon': 'Каньоны',
                'cave': 'Пещеры',
                'dungeon': 'Подземелья'
            };

            let selectedLocationId = '{{ old('location_id') }}';
            let selectedLocationName = '';

            // Найти название выбранной локации
            if (selectedLocationId) {
                Object.keys(locations).forEach(type => {
                    locations[type].forEach(location => {
                        if (location.id == selectedLocationId) {
                            selectedLocationName = location.name;
                        }
                    });
                });
                $('#location_search').val(selectedLocationName);
            }

            // Функция для отображения выпадающего списка
            function showDropdown(searchTerm = '') {
                const dropdown = $('#location_dropdown');
                dropdown.empty();

                let hasResults = false;
                const searchLower = searchTerm.toLowerCase();

                Object.keys(locations).forEach(type => {
                    const typeLocations = locations[type].filter(location =>
                        location.name.toLowerCase().includes(searchLower)
                    );

                    if (typeLocations.length > 0) {
                        hasResults = true;

                        // Заголовок группы
                        const groupHeader = $('<div class="location-group-header"></div>')
                            .text(locationTypeNames[type] || type);
                        dropdown.append(groupHeader);

                        // Локации в группе
                        typeLocations.forEach(location => {
                            const option = $('<div class="location-option"></div>')
                                .text(location.name)
                                .data('id', location.id)
                                .data('name', location.name);

                            if (location.id == selectedLocationId) {
                                option.addClass('selected');
                            }

                            option.click(function () {
                                selectedLocationId = $(this).data('id');
                                selectedLocationName = $(this).data('name');
                                $('#location_id').val(selectedLocationId);
                                $('#location_search').val(selectedLocationName);
                                dropdown.hide();
                            });

                            dropdown.append(option);
                        });
                    }
                });

                if (!hasResults) {
                    dropdown.append('<div class="no-results">Локации не найдены</div>');
                }

                dropdown.show();
            }

            // Обработчики событий
            $('#location_search').on('focus', function () {
                showDropdown($(this).val());
            });

            $('#location_search').on('input', function () {
                showDropdown($(this).val());
            });

            // Скрыть выпадающий список при клике вне его
            $(document).on('click', function (e) {
                if (!$(e.target).closest('.location-selector').length) {
                    $('#location_dropdown').hide();
                }
            });

            // Очистка поля при изменении текста
            $('#location_search').on('input', function () {
                const currentValue = $(this).val();
                if (currentValue !== selectedLocationName) {
                    selectedLocationId = '';
                    selectedLocationName = '';
                    $('#location_id').val('');
                }
            });
        });
    </script>
</body>

</html>