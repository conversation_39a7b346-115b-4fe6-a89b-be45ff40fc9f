<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- CSRF-токен для AJAX-запросов --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Регистрация</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif  ">
    {{-- Основной контейнер --}}

    <div class="max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg "
        style="background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7))">
        {{-- блок сообщений и событий --}}

        <!-- <div class="text-center flex justify-center space-x-1 shadow-lg">
            <span class="block text-lg text-[#20B2AA]">📩 <span class=" text-lg text-[#3CB371]">(+1)</span></span>
            <span class="block text-lg text-[#20B2AA]">📅 <span class=" text-lg text-[#3CB371]"></span></span>
            <span class="block text-lg text-[#20B2AA]">📅 <span class=" text-lg text-[#3CB371]"></span></span>
            <span class="block text-lg text-[#20B2AA]">📅 <span class=" text-lg text-[#3CB371]"></span></span>
        </div> --> {{-- HP/MP блок --}}



        <div class="flex justify-between items-center text-[#d9d3b8]  rounded-md pt-1.5 shadow-inner">
            {{-- HP с иконкой --}}
            <div class="flex items-center">
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                    <span class="text-[#FF6347] text-xs">❤️</span>
                </div>
                <div class="flex flex-col">
                    <!--
                        Русский комментарий:
                        Здесь отображается прогресс-бар HP (здоровья) персонажа.
                        Используем только актуальное значение HP из getActualResources(), чтобы всегда показывать правильное количество здоровья.
                    -->
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full"
                            style="width: calc({{ $actualResources['current_hp'] }}/{{ $userProfile->max_hp }}*100%)">
                        </div>
                    </div>
                    <span class="text-[#e5b769] text-[12px]">
                        {{ $actualResources['current_hp'] }}/{{ $userProfile->max_hp }}
                        <!--
                            Русский комментарий:
                            Отображаем актуальное количество HP (здоровья) и максимум, используя только getActualResources().
                        -->
                    </span>
                </div>
            </div>

            {{-- Уведомления --}}
            <div class="flex space-x-1">
                {{-- Почта --}}
                <a href="/mail" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📩</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        3
                    </span>
                </a>

                {{-- События --}}
                <a href="/events" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">🎉</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        3
                    </span>
                </a>

                {{-- Задания --}}
                <a href="/quests" class="relative group">
                    <div
                        class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                        <span class="block text-xs">📜</span>
                    </div>
                    <span
                        class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center">
                        5
                    </span>
                </a>
            </div>

            {{-- MP с иконкой --}}
            <div class="flex items-center">
                <div class="flex flex-col items-end">
                    <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                        <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full"
                            style="width: calc(5/130*100%)"></div>
                    </div>
                    <span class="text-[#e5b769] text-[12px]">5/130</span>
                </div>
                <div
                    class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] ml-1">
                    <span class="text-[#1E90FF] text-xs">🔮</span>
                </div>
            </div>
        </div>

        {{-- Шкала прогресса --}}
        <div class="w-full bg-[#6b6658] mt-0.5 h-0.5 rounded-full mb-5">
            <div class="bg-[#e5b769] h-0.5 rounded-full" style="width: 50%;"></div>
            <div class="flex gap-1 items-center ">
                {{-- Золото --}}
                <div class="flex items-center ">
                    <img src="{{ asset('assets/goldIcon.png') }}" alt="Золото" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#e5b769]">
                        {{ number_format($userProfile->gold, 0, ',', ' ') }}
                    </span>
                </div>
                {{-- Серебро --}}
                <div class="flex items-center ">
                    <img src="{{ asset('assets/silverIcon.png') }}" alt="Серебро" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#c0c0c0]">
                        {{ number_format($userProfile->silver, 0, ',', ' ') }}
                    </span>
                </div>
                {{-- Бронза --}}
                <div class="flex items-center ">
                    <img src="{{ asset('assets/bronzeIcon.png') }}" alt="Бронза" class="w-4 h-4">
                    <span class="text-sm font-medium text-[#cd7f32]">
                        {{ number_format($userProfile->bronze, 0, ',', ' ') }}
                    </span>
                </div>
            </div>

        </div>
        <div class="text-center flex justify-center space-x-1  ">
            @if (session('welcome_message'))
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    {{ session('welcome_message') }}
                </div>
            @endif
        </div>
        {{-- Блок изображения --}}
        <div class="mb-2">
            <x-breadcrumbs :breadcrumbs="$breadcrumbs" />
            {{-- Отображение активных эффектов --}}
            <div class="active-effects">
                @php
                    $isStunned = $userEffects->contains(function ($effect) {
                        return $effect->isActive() && $effect->isStunEffect();
                    });
                @endphp

                @if ($userEffects->isEmpty())
                    <p class="text-gray-400">Нет активных эффектов.</p>
                @else
                    <div class="flex flex-row flex-wrap  pl-0 ml-0 gap-1 items-start">
                        @foreach ($userEffects as $effect)
                            <div class="flex flex-col items-center justify-center text-center w-4">
                                <img src="{{ asset($effect->skill->icon ?? 'assets/default_effect.png') }}"
                                    alt="{{ $effect->skill->name }}"
                                    class="w-4 h-4 {{ $effect->skill_id == 10 ? 'animate-pulse' : ($effect->skill->type === 'debuff' ? 'text-red-400' : 'text-green-400') }}">
                                <span
                                    class="text-[10px] w-full {{ $effect->skill_id == 10 ? 'text-yellow-400' : ($effect->skill->type === 'debuff' ? 'text-red-400' : 'text-green-400') }}">
                                    {{ round($effect->remaining_duration) }}с
                                </span>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
            <x-layout.location-name title="Песчаный Оплот" />
        </div>
        <!-- <div class="w-full h-40 bg-[#3b3a33] rounded-lg mb-6 flex items-center justify-center border border-[#a6925e]">
            <span class="text-[#d9d3b8] font-semibold">Изображение локации</span>
        </div> -->
        <!-- <div
            class="w-full h-20 bg-[#3b3a33] rounded-lg mt-4 mb-4 flex items-center justify-center border border-[#a6925e] shadow-lg ">
            <img src="{{ asset('assets/test4.jpeg') }}" alt="Изображение локации"
                class="w-full h-full object-cover rounded-lg opacity-55">
        </div> -->
        {{-- Главная --}}

        {{-- Ссылки/кнопки --}}
        {{-- Инвентарь --}}
        <div class="mt-2">

            <div
                class="bg-[#3b3a33] border border-[#a6925e] rounded-lg shadow-md w-full p-1 mb-4 flex justify-between items-center">
                {{-- Альянс --}}
                <div class="flex items-center space-x-1">

                    <span class="text-xs text-yellow-300">⚔️{{ $solWarriors }}</span>
                    <span class="text-xs text-gray-300">🧙🏻‍♂{{ $solMages }}</span>
                    <span class="text-xs text-gray-400">⚜️{{ $solKnights }}</span>
                </div>

                {{-- Нейтралы --}}
                <div class="flex items-center space-x-1">
                    <div class="w-4 h-4 bg-[#4a4a3d] rounded-full flex items-center justify-center shadow">
                        <i class="fa-solid fa-handshake text-green-400 text-xs"></i>
                    </div>
                    <span class="text-xs text-green-400"></span>
                </div>

                {{-- Орда --}}
                <div class="flex items-center space-x-1">

                    <span class="text-xs text-red-400">⚔️{{ $lunWarriors }}</span>
                    <span class="text-xs text-gray-300">🧙🏻‍♂{{ $lunMages }}</span>
                    <span class="text-xs text-gray-400">⚜️{{ $lunKnights }}</span>
                </div>
            </div>
            <div class="grid grid-cols-3 gap-2 w-full mb-2">
                {{-- Деревня слева --}}
                {{-- Деревня Solarius --}}
                @php
                    $userRace = auth()->user()->profile->race;
                @endphp

                {{-- Деревня Solarius --}}
                @php
                    $userRace = auth()->user()->profile->race;
                @endphp

                <x-battle.outposts.village-block :village="$solariusVillage" :userRace="$userRace"
                    villageName="Западная" routePrefix="battle.outposts.sandy_stronghold" />

                {{-- Обелиск --}}
                <form action="{{ route('battle.outposts.sandy_stronghold.select_mob') }}" method="POST"
                    class="flex flex-col items-center">
                    @csrf
                    <button type="submit" class="flex flex-col items-center">
                        <img src="{{ asset('assets/obelisk.png') }}" alt="Обелиск" class="w-8 h-12 mb-1">
                        {{-- Название обелиска --}}
                        <div class="text-center text-[10px] text-[#e5b769] font-semibold mb-0.5">
                            Обелиск
                        </div>

                        {{-- Полоска прогресса обелиска --}}
                        <div class="w-24 bg-[#2a2721] border border-[#514b3c] rounded-md relative overflow-hidden">
                            @php
                                $progressPercent = 90; // Используем фиксированное значение, так как в оригинале было 90%
                                $progressColor = $progressPercent > 70 ? 'bg-gradient-to-r from-[#4a5c2f] to-[#5a6d3f]' :
                                    ($progressPercent > 30 ? 'bg-gradient-to-r from-[#8c7a45] to-[#9c8a55]' :
                                        'bg-gradient-to-r from-[#8a4a4a] to-[#9a5a5a]');
                                $textColor = $progressPercent > 70 ? 'text-[#a3e635]' :
                                    ($progressPercent > 30 ? 'text-[#e5b769]' : 'text-[#ff6b6b]');
                            @endphp
                            <div class="{{ $progressColor }} h-5" style="width: {{ $progressPercent }}%;"></div>
                            <div
                                class="absolute inset-0 flex justify-center items-center text-[11px] font-bold {{ $textColor }}">
                                90%
                            </div>
                        </div>
                    </button>
                </form>

                {{-- Деревня справа --}}
                @if ($lunariusVillage)
                    <form
                        action="{{ route('battle.outposts.sandy_stronghold.village.attack', ['id' => $lunariusVillage->id]) }}"
                        method="POST" class="flex flex-col items-center">
                        @csrf

                        {{-- Динамическая иконка в зависимости от расы, которая контролирует деревню --}}
                        @if ($lunariusVillage->race !== $userRace)
                            <button type="submit" class="flex flex-col items-center">
                                <img src="{{ $lunariusVillage->race === 'lunarius' ? asset('assets/postLunar.png') : asset('assets/postSolarius.png') }}"
                                    alt="Восточная часть" class="w-8 h-8 mb-1">
                            </button>
                        @else
                            <img src="{{ $lunariusVillage->race === 'lunarius' ? asset('assets/postLunar.png') : asset('assets/postSolarius.png') }}"
                                alt="Восточная часть" class="w-8 h-8 mb-1 opacity-50">
                        @endif

                        {{-- Название деревни --}}
                        <div class="text-center text-[10px] text-[#e5b769] font-semibold mb-0.5">
                            Восточная
                        </div>

                        {{-- Полоска здоровья --}}
                        <div class="w-24 bg-[#2a2721] border border-[#514b3c] rounded-md relative overflow-hidden">
                            @php
                                $healthPercent = ($lunariusVillage->health / $lunariusVillage->max_health) * 100;
                                $healthColor = $healthPercent > 70 ? 'bg-gradient-to-r from-[#4a5c2f] to-[#5a6d3f]' :
                                    ($healthPercent > 30 ? 'bg-gradient-to-r from-[#8c7a45] to-[#9c8a55]' :
                                        'bg-gradient-to-r from-[#8a4a4a] to-[#9a5a5a]');
                                $textColor = $healthPercent > 70 ? 'text-[#a3e635]' :
                                    ($healthPercent > 30 ? 'text-[#e5b769]' : 'text-[#ff6b6b]');
                                $formattedHealth = number_format($lunariusVillage->health, 0, ',', ' ');
                                $formattedMaxHealth = number_format($lunariusVillage->max_health, 0, ',', ' ');
                            @endphp
                            <div class="{{ $healthColor }} h-5" style="width: {{ $healthPercent }}%;"></div>
                            <div
                                class="absolute inset-0 flex justify-center items-center text-[11px] font-bold {{ $textColor }}">
                                {{ $formattedHealth }}/{{ $formattedMaxHealth }}
                            </div>
                        </div>
                    </form>
                @endif
            </div>

            {{-- Блок действий с целью --}}
            {{-- Блок действий с целью --}}
            <div
                class="bg-gradient-to-b from-[#3b3a33] to-[#2a2922] rounded-lg border-2 border-[#8c7a55] shadow-md shadow-[0px_4px_10px_rgba(0,0,0,0.3)] overflow-hidden mt-4">
                {{-- Заголовок блока --}}
                <div
                    class="text-center py-1 text-[#e9d5a0] font-bold text-sm border-b-2 border-[#8c7a55] bg-gradient-to-b from-[#5a4d36] to-[#3a321c]">
                    Действия
                </div>

                {{-- Блок для флеш-сообщений --}}
                <x-game-flash-messages />

                <div class="p-2 space-y-2">
                    @if ($isStunned)
                        {{-- Сообщение о стане отображается в красной полоске сверху --}}
                    @else
                        {{-- Универсальный блок действий с целью (моб, игрок, бот) --}}
                        @if (in_array(Auth::user()->current_target_type, ['mob', 'player', 'bot']) && $target)
                            <!--
                                                                                                                                    Русский комментарий:
                                                                                                                                    Этот блок отображает подробную информацию о текущей цели (моб, игрок или бот).
                                                                                                                                    Включает иконку, имя, HP, эффекты, кнопку атаки и прогресс-бар.
                                                                                                                                    Для разных типов целей меняется только маршрут формы и скрытые поля.
                                                                                                                                -->
                            <div class="space-y-1.5">
                                {{-- Блок иконки и информации о цели --}}
                                <div
                                    class="flex items-center bg-gradient-to-b from-[#333025] to-[#28261d] p-1.5 rounded-md border border-[#8c7a55]">
                                    {{-- Иконка цели --}}
                                    @if (Auth::user()->current_target_type === 'mob')
                                        <img src="{{ asset($target->icon ?? 'wolfIcon.png') }}" alt="{{ $target->name }}"
                                            class="w-6 h-6 rounded-md">
                                    @elseif(Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot')
                                        <div
                                            class="w-6 h-6 rounded-md bg-gradient-to-b from-[#913838] to-[#762323] border border-[#c07777] flex items-center justify-center text-white font-bold text-xs">
                                            {{ Auth::user()->current_target_type === 'player' ? 'PvP' : 'PvP' }}
                                        </div>
                                    @endif
                                    <div class="ml-1.5 flex-1">
                                        {{-- Имя и HP цели --}}
                                        <div class="text-[#e9d5a0] text-xs font-semibold mb-0.5 flex justify-between">
                                            {{ $target->name }}
                                            <span
                                                class="text-[10px] {{ Auth::user()->current_target_type === 'mob' ? 'text-[#9c8d69]' : 'text-red-400' }}">
                                                {{--
                                                Русский комментарий:
                                                Отображаем HP и максимум для цели. Для моба — свои поля, для игрока используем
                                                метод getActualResources(), который рассчитывает актуальное HP на лету.
                                                --}}
                                                @if (Auth::user()->current_target_type === 'mob')
                                                    {{ $target->hp }}/{{ $target->max_hp }}
                                                @elseif(Auth::user()->current_target_type === 'player')
                                                    @php
                                                        // Используем try-catch для корректной обработки возможных ошибок
                                                        try {
                                                            // Получаем актуальные данные о HP из Redis напрямую через PlayerHealthService
                                                            $playerHealthService = app(\App\Services\PlayerHealthService::class);
                                                            $currentHP = $playerHealthService->getCurrentHP($target);
                                                            $targetActualResources = [
                                                                'current_hp' => $currentHP,
                                                                'current_mp' => $target->profile->current_mp ?? 0,
                                                            ];
                                                        } catch (\Exception $e) {
                                                            // При ошибке используем значения по умолчанию
                                                            $targetActualResources = [
                                                                'current_hp' => $target->profile->current_hp ?? $target->profile->hp ?? 0,
                                                                'current_mp' => $target->profile->current_mp ?? $target->profile->mp ?? 0,
                                                            ];
                                                            \Illuminate\Support\Facades\Log::error(
                                                                'Ошибка получения актуальных ресурсов цели: ' . $e->getMessage(),
                                                                ['target_id' => $target->id, 'profile_exists' => isset($target->profile)]
                                                            );
                                                        }
                                                    @endphp
                                                    ❤️
                                                    {{ $targetActualResources['current_hp'] }}/{{ $target->profile->max_hp ?? 100 }}
                                                @elseif(Auth::user()->current_target_type === 'bot')
                                                    @php
                                                        // Используем try-catch для обработки возможных ошибок
                                                        try {
                                                            // Используем метод getActualResources для получения актуального HP
                                                            $botActualResources = $target->getActualResources();
                                                        } catch (\Exception $e) {
                                                            // Если произошла ошибка, используем значения по умолчанию
                                                            $botActualResources = [
                                                                'current_hp' => $target->hp ?? 0,
                                                                'current_mp' => $target->mp ?? 0,
                                                            ];
                                                            \Illuminate\Support\Facades\Log::error(
                                                                'Ошибка получения актуальных ресурсов бота: ' .
                                                                $e->getMessage(),
                                                            );
                                                        }
                                                    @endphp
                                                    ❤️
                                                    {{ $botActualResources['current_hp'] }}/{{ $target->max_hp ?? 100 }}
                                                @endif
                                            </span>
                                        </div>
                                        {{-- Полоска HP цели --}}
                                        <div
                                            class="flex items-center {{ Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot' ? 'mt-0.5' : '' }}">
                                            <div
                                                class="w-full h-2 bg-[#1a1915] rounded border border-[#46423a] overflow-hidden">
                                                <div class="h-full"
                                                    style="width:
                                                                                                                        @if (Auth::user()->current_target_type === 'mob') {{ ($target->hp / $target->max_hp) * 100 }}%
                                                                                                                        @elseif(Auth::user()->current_target_type === 'player')
                                                                                                                            {{ ($targetActualResources['current_hp'] / ($target->profile->max_hp ?? 100)) * 100 }}%
                                                                                                                        @elseif(Auth::user()->current_target_type === 'bot')
                                                                                                                        {{ ($botActualResources['current_hp'] / ($target->max_hp ?? 100)) * 100 }}% @endif
                                                                                                                    ; background-color:
                                                                                                                        @php
                                                                                                                            $hp = 0;
                                                                                                                            $max = 1;
                                                                                                                            if (Auth::user()->current_target_type === 'mob') {
                                                                                                                                $hp = $target->hp;
                                                                                                                                $max = $target->max_hp;
                                                                                                                            } elseif (Auth::user()->current_target_type === 'player') {
                                                                                                                                $hp = $targetActualResources['current_hp'];
                                                                                                                                $max = $target->profile->max_hp ?? 100;
                                                                                                                            } elseif (Auth::user()->current_target_type === 'bot') {
                                                                                                                                $hp = $botActualResources['current_hp'];
                                                                                                                                $max = $target->max_hp ?? 100;
                                                                                                                        } @endphp
                                                                                                                        {{ $hp > $max * 0.7 ? '#4CAF50' : ($hp > $max * 0.3 ? '#FFC107' : '#F44336') }};">
                                                </div>
                                            </div>
                                        </div>
                                        {{-- Эффекты на цели --}}
                                        @php
                                            // Русский комментарий:
                                            // Получаем эффекты для цели в зависимости от типа
                                            if (Auth::user()->current_target_type === 'mob') {
                                                $effects = \App\Models\ActiveEffect::where('target_type', 'mob')
                                                    ->where('target_id', $target->id)
                                                    ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                                                    ->with('skill')
                                                    ->get();
                                            } elseif (Auth::user()->current_target_type === 'player') {
                                                $effects = \App\Models\ActiveEffect::where(
                                                    'target_type',
                                                    'App\\Models\\User',
                                                )
                                                    ->where('target_id', $target->id)
                                                    ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                                                    ->with('skill')
                                                    ->get();
                                            } elseif (Auth::user()->current_target_type === 'bot') {
                                                $effects = \App\Models\ActiveEffect::where('target_type', 'bot')
                                                    ->where('target_id', $target->id)
                                                    ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                                                    ->with('skill')
                                                    ->get();
                                            } else {
                                                $effects = collect();
                                            }
                                        @endphp
                                        @if ($effects->isNotEmpty())
                                            <div class="flex flex-wrap gap-0.5 mt-1">
                                                @foreach ($effects as $effect)
                                                    <div class="relative group">
                                                        <img src="{{ asset($effect->skill->icon ?? 'assets/default_effect.png') }}"
                                                            alt="{{ $effect->skill->name }}"
                                                            class="w-3.5 h-3.5 rounded-full {{ $effect->skill_id == 10 ? 'animate-pulse ring-1 ring-yellow-400/60' : ($effect->skill->type === 'debuff' ? 'ring-1 ring-red-400/60' : 'ring-1 ring-green-400/60') }}" />
                                                        <div class="absolute bottom-full right-0 mb-1 hidden group-hover:block z-50">
                                                            <div
                                                                class="bg-black/90 text-white text-[10px] px-1.5 py-0.5 rounded whitespace-nowrap">
                                                                {{ $effect->skill->name }}
                                                                ({{ round($effect->remaining_duration) }}с)
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                {{-- Кнопка атаки и полоса заряда --}}
                                {{-- Attack button and charge bar --}}
                                <form id="attackForm" {{-- Определяем URL для отправки формы в зависимости от типа цели --}}
                                    {{-- Determine the form submission URL based on the target type --}}
                                    action="@if (Auth::user()->current_target_type === 'mob') {{-- Если цель - моб --}} {{ route('battle.outposts.sandy_stronghold.attack') }}@elseif(Auth::user()->current_target_type === 'player'){{-- Если цель - игрок --}}{{ route('battle.outposts.sandy_stronghold.attack_any_player') }}@elseif(Auth::user()->current_target_type === 'bot'){{-- Если цель - бот, используем тот же маршрут, что и для игрока --}}{{ route('battle.outposts.sandy_stronghold.attack_any_player') }}@endif"
                                    method="POST">
                                    @csrf
                                    {{-- Скрытые поля для передачи ID и типа цели при атаке игрока или бота --}}
                                    {{-- Hidden fields to pass target ID and type when attacking a player or bot --}}
                                    @if (Auth::user()->current_target_type === 'bot')
                                        <input type="hidden" name="target_type" value="bot"> {{-- Тип цели: бот --}}
                                        <input type="hidden" name="target_id" value="{{ $target->id }}"> {{-- ID цели-бота --}}
                                    @elseif(Auth::user()->current_target_type === 'player')
                                        <input type="hidden" name="target_type" value="user"> {{-- Тип цели: игрок (user в запросе)
                                        --}}
                                        <input type="hidden" name="target_id" value="{{ $target->id }}"> {{-- ID цели-игрока --}}
                                    @endif
                                    <button id="attackButton" type="submit"
                                        class="w-full py-2
                                                                                                                                                @if (Auth::user()->current_target_type === 'mob') bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777]
                                                                                                                                                @else
                                                                                                                                                bg-gradient-to-b from-[#913838] to-[#762323] border-2 border-[#c07777] @endif
                                                                                                                                                rounded-md text-white text-sm font-bold shadow-md
                                                                                                                                                hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e] transition-all text-center">
                                        {{--
                                        Русский комментарий:
                                        Текст кнопки одинаков для всех типов целей
                                        --}}
                                        @if (Auth::user()->current_target_type === 'mob')
                                            Атаковать
                                        @elseif(Auth::user()->current_target_type === 'player')
                                            Атаковать
                                        @else
                                            Атаковать
                                        @endif
                                    </button>
                                    {{-- Полоса заряда атаки --}}
                                    <div
                                        class="w-full h-2 bg-[#222019] rounded-sm mt-1.5 border border-[#46423a] overflow-hidden">
                                        <div id="progressBar" class="h-full bg-[#F44336] rounded-sm transition-all"
                                            style="width: 0%;"></div>
                                    </div>
                                </form>
                                {{-- Кнопка смены цели (только для игрока и бота) --}}
                                @if (Auth::user()->current_target_type === 'player' || Auth::user()->current_target_type === 'bot')
                                    <form action="{{ route('battle.outposts.sandy_stronghold.change_player_target') }}"
                                        method="POST">
                                        @csrf
                                        <button type="submit"
                                            class="w-full py-1.5 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b] transition-all text-center">
                                            Сменить цель
                                        </button>
                                    </form>
                                @endif
                            </div>
                        @else
                            {{-- Нет цели - показываем заголовок блока и кнопку выбора случайного игрока --}}
                            <div
                                class="text-center text-[#e9d5a0] p-2 bg-gradient-to-b from-[#333025] to-[#28261d] rounded-md border border-[#8c7a55] mb-2">
                                Выберите цель для атаки
                            </div>
                            <form action="{{ route('battle.outposts.sandy_stronghold.attack_any_player') }}" method="POST">
                                @csrf
                                <button type="submit"
                                    class="w-full py-2 bg-gradient-to-b from-[#7a6745] to-[#5a4d36] border-2 border-[#a6925e] rounded-md text-[#e9d5a0] text-xs font-semibold shadow-md hover:from-[#8c774f] hover:to-[#6c5e43] active:from-[#64543a] active:to-[#483c2b] transition-all text-center">
                                    Бить любого
                                </button>
                            </form>
                        @endif

                        {{-- Дополнительные кнопки (Ответный удар) --}}
                        <div class="pt-1 space-y-1.5">
                            @php
                                $currentUser = Auth::user();
                                $currentLocation = $currentUser->statistics->current_location ?? 'Неизвестно';
                                $lastAttacker = $currentUser->last_attacker_id
                                    ? \App\Models\User::where('id', $currentUser->last_attacker_id)
                                        ->whereHas('statistics', function ($query) use ($currentLocation) {
                                            $query->where('current_location', $currentLocation);
                                        })
                                        ->where('last_activity_timestamp', '>=', now()->subMinutes(5)->timestamp)
                                        ->with('profile', 'statistics')
                                        ->first()
                                    : null;

                                // Проверяем, не выбран ли уже атакующий в качестве цели
                                $isAttackerAlreadyTargeted =
                                    $lastAttacker &&
                                    $currentUser->current_target_type === 'player' &&
                                    $currentUser->current_target_id == $lastAttacker->id;
                            @endphp

                            @if ($lastAttacker && $lastAttacker->profile && $lastAttackerResources && $lastAttackerResources['current_hp'] > 0 && !$isAttackerAlreadyTargeted)
                                @php
                                    // Используем try-catch для обработки возможных ошибок при получении актуальных ресурсов
                                    try {
                                        $lastAttackerResources = $lastAttacker->profile->getActualResources();
                                    } catch (\Exception $e) {
                                        $lastAttackerResources = [
                                            'current_hp' => $lastAttacker->profile->hp ?? 0,
                                            'current_mp' => $lastAttacker->profile->mp ?? 0,
                                        ];
                                        \Illuminate\Support\Facades\Log::error(
                                            'Ошибка получения актуальных ресурсов атакующего: ' . $e->getMessage(),
                                        );
                                    }

                                    $hpPercent =
                                        ($lastAttackerResources['current_hp'] / $lastAttacker->profile->max_hp) * 100;
                                    // Определяем цвет индикатора в зависимости от % здоровья
                                    $hpColor =
                                        $hpPercent > 70
                                        ? 'bg-green-600'
                                        : ($hpPercent > 30
                                            ? 'bg-yellow-500'
                                            : 'bg-red-600');
                                @endphp

                                <form action="{{ route('battle.outposts.sandy_stronghold.retaliate') }}" method="POST">
                                    @csrf
                                    <button type="submit"
                                        class="w-full py-2 bg-gradient-to-b from-[#913838] to-[#762323]
                                                                                                            border-2 border-[#c07777] rounded-md text-white text-xs font-semibold shadow-md
                                                                                                            hover:from-[#a13e3e] hover:to-[#852929] active:from-[#7a2e2e] active:to-[#5e1e1e]
                                                                                                            transition-all duration-300 text-center group relative overflow-hidden">

                                        {{-- Фоновая анимация при наведении --}}
                                        <span
                                            class="absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b6b40] to-transparent opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>

                                        <div class="flex items-center justify-center relative z-10">
                                            {{-- Иконка атаки --}}
                                            <span class="mr-1 text-red-300">⚔️</span>

                                            {{-- Текст кнопки --}}
                                            <span class="font-bold">Бить в ответ</span>

                                            {{-- Имя и HP противника в стильном контейнере --}}
                                            <div
                                                class="ml-2 flex items-center bg-[#6b2c2c] px-2 py-0.5 rounded border border-[#c07777] shadow-inner">
                                                <span class="text-[10px] text-yellow-200 mr-1">{{ $lastAttacker->name }}</span>

                                                {{-- Мини-полоска HP --}}
                                                <div class="w-14 h-1.5 bg-[#421a1a] rounded-full overflow-hidden flex-shrink-0">
                                                    <div class="{{ $hpColor }} h-full" style="width: {{ $hpPercent }}%;"></div>
                                                </div>

                                                {{-- Числовое значение HP --}}
                                                <span class="text-[9px] text-gray-300 ml-1">
                                                    {{ $lastAttackerResources['current_hp'] }}<span
                                                        class="text-gray-500">/{{ $lastAttacker->profile->max_hp }}</span>
                                                </span>
                                            </div>
                                        </div>
                                    </button>
                                </form>
                            @endif
                        </div>
                    @endif {{-- Конец проверки $isStunned --}}
                </div>
            </div>
        </div>

        <x-user.quick-potion-bar :compact="true" />

        {{-- Панель умений --}}
        <x-battle.skills-panel routePrefix="battle.outposts.sandy_stronghold" :isStunned="$isStunned ?? false" />

        {{-- Логи --}}
        <div class="mt-4 w-full">
            <div
                class="w-full bg-[#232218] border border-[#a6925e] rounded-md shadow-lg overflow-hidden max-w-md mx-auto">
                {{-- Заголовок журнала событий --}}
                <div
                    class="flex items-center justify-center bg-gradient-to-r from-[#3d3928] to-[#4a4532] border-b border-[#a6925e] px-1 py-2 relative">
                    <div class="absolute left-0 top-0 bottom-0 w-6 flex items-center justify-center">
                        <svg class="w-4 h-4 text-[#c9aa6e]" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-[#ffd046] text-sm font-bold tracking-wide text-center px-6">Журнал боя</h3>
                    <div class="absolute right-0 top-0 bottom-0 w-6 flex items-center justify-center">
                        <svg class="w-4 h-4 text-[#c9aa6e]" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                            </path>
                        </svg>
                    </div>
                </div>

                {{-- Контейнер для логов --}}
                <div id="battleLogsContainer" class="max-h-44 overflow-y-auto custom-scrollbar"
                    style="scrollbar-width: thin; scrollbar-color: #a6925e #232218;">
                    @forelse($battleLogs as $log)
                                    <div
                                        class="px-3 py-1.5 border-b border-[#3b3a2a] transition-colors duration-150 hover:bg-[#2a2920]">
                                        <div class="flex items-center justify-between">
                                            {{-- Временная метка --}}
                                            <span
                                                class="inline-block w-7 text-xs text-[#a6925e] mr-2 font-mono whitespace-nowrap text-left">
                                                {{ \Carbon\Carbon::parse($log['timestamp'] ?? now())->format('H:i') }}
                                            </span>

                                            {{-- Содержимое лога --}}
                                            <div class="flex-1 min-w-0">
                                                <div class="text-xs leading-tight break-words
                                                        {{ $log['type'] === 'success'
                        ? 'text-[#4ADE80]'
                        : ($log['type'] === 'danger'
                            ? 'text-[#FF6B4A]'
                            : ($log['type'] === 'warning'
                                ? 'text-[#FFD046]'
                                : 'text-[#e0e0d0]')) }}">
                                                    {!! $log['message'] !!}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                    @empty
                        <div class="flex flex-col items-center justify-center py-6 px-2 text-center">
                            <svg class="w-5 h-5 mb-1 text-[#8b8b70]" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <p class="text-xs text-[#a09a80]">Журнал пуст</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>


        {{-- Нижние кнопки навигации внутри основного контейнера --}}
        <div class="text-center px-2 py-2 flex justify-center space-x-2">
            {{-- Рюкзак --}}
            <a href="{{ route('inventory.index') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Рюкзак
            </a>
            {{-- Персонаж --}}
            <a href="{{ route('user.profile') }}"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Персонаж
            </a>
            {{-- Гильдия --}}
            <a href="#"
                class="bg-[#c4a76d] text-[#2f2d2b] py-1 px-1 rounded shadow-lg hover:bg-[#d4b781] transition duration-300">
                Гильдия
            </a>
        </div>

        {{-- Футер - внутри основного контейнера --}}
        <footer
            class="bg-gradient-to-b from-[#2d2924] to-[#1f1c18] border-t border-[#514b3c] flex-shrink-0 shadow-inner w-full">
            {{-- Декоративный верхний бордюр --}}
            <div class="relative h-3 w-full overflow-hidden">
                <div class="absolute inset-0 bg-[#211f1a]"></div>
                {{-- Центральный орнамент --}}
                <div class="absolute left-1/2 transform -translate-x-1/2 top-0 w-32 h-3 overflow-hidden">
                    <div
                        class="absolute inset-x-0 top-0 h-px bg-gradient-to-b from-[#a6925e] to-transparent opacity-80">
                    </div>
                    <div
                        class="absolute inset-x-4 top-1 h-px bg-gradient-to-b from-[#a6925e] to-transparent opacity-60">
                    </div>
                    <div
                        class="absolute inset-x-8 top-2 h-px bg-gradient-to-b from-[#8a775f] to-transparent opacity-40">
                    </div>
                </div>
                {{-- Боковые элементы --}}
                <div class="absolute left-2 top-1 w-2 h-2 rounded-full border border-[#a6925e] opacity-70"></div>
                <div class="absolute right-2 top-1 w-2 h-2 rounded-full border border-[#a6925e] opacity-70"></div>
            </div>

            {{-- Мобильная навигация --}}
            <div
                class="{{ auth()->check() && auth()->user()->role === 'admin' ? 'grid-cols-4' : 'grid-cols-3' }} grid px-24 py-1 max-w-md mx-auto">
                {{-- Чат --}}
                <a href="{{ route('chat.index') }}" class="flex flex-col items-center group transition duration-300">
                    <div
                        class="w-10 h-10 rounded-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border border-[#514b3c] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#a6925e] group-hover:shadow-md">
                        <svg class="w-5 h-5 text-[#d3c6a6] group-hover:text-[#e5b769]" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                    </div>
                    <span
                        class="text-[10px] text-[#d3c6a6] mt-1 group-hover:text-[#e5b769] transition-colors duration-300">Чат</span>
                </a>

                {{-- Форум --}}
                <a href="#" class="flex flex-col items-center group transition duration-300">
                    <div
                        class="w-10 h-10 rounded-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border border-[#514b3c] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#a6925e] group-hover:shadow-md">
                        <svg class="w-5 h-5 text-[#d3c6a6] group-hover:text-[#e5b769]" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path
                                d="M17 8h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2v4l-4-4H9a1.994 1.994 0 0 1-1.414-.586m0 0L11 14h4a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2v4l.586-.586z">
                            </path>
                        </svg>
                    </div>
                    <span
                        class="text-[10px] text-[#d3c6a6] mt-1 group-hover:text-[#e5b769] transition-colors duration-300">Форум</span>
                </a>

                {{-- Новости --}}
                <a href="#" class="flex flex-col items-center group transition duration-300">
                    <div
                        class="w-10 h-10 rounded-full bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border border-[#514b3c] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#a6925e] group-hover:shadow-md">
                        <svg class="w-5 h-5 text-[#d3c6a6] group-hover:text-[#e5b769]" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path
                                d="M19 20H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v1M15 7h4a2 2 0 0 1 2 2v9.5a1.5 1.5 0 0 1-3 0V9h-3v1">
                            </path>
                        </svg>
                    </div>
                    <span
                        class="text-[10px] text-[#d3c6a6] mt-1 group-hover:text-[#e5b769] transition-colors duration-300">Новости</span>
                </a>

                @if (auth()->check() && auth()->user()->role === 'admin')
                    <a href="{{ route('admin.dashboard') }}"
                        class="flex flex-col items-center group transition duration-300">
                        <div
                            class="w-10 h-10 rounded-full bg-gradient-to-b from-[#413a2d] to-[#2e271d] border border-[#7a6c51] flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 group-hover:border-[#e5b769] group-hover:shadow-lg relative animate-pulse">
                            <svg class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" fill="currentColor"
                                viewBox="0 0 24 24">
                                <path
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543.826 3.31 2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                                </path>
                                <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <span
                            class="text-[10px] text-[#e5b769] mt-1 group-hover:text-[#f0d89e] transition-colors duration-300">Админка</span>
                    </a>
                @endif
            </div>

            {{-- Информационные карточки --}}
            <div class="grid grid-cols-2 gap-2 px-3 max-w-md mx-auto">
                {{-- Карточка онлайн-статуса --}}
                <a href="/online"
                    class="group relative flex items-center px-3 py-2 bg-gradient-to-br from-[#2a2621] to-[#201d18] rounded-md border border-[#514b3c] hover:border-[#a6925e] transition-all duration-300 overflow-hidden">
                    {{-- Декоративные элементы --}}
                    <div
                        class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
                    </div>
                    <div
                        class="absolute right-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
                    </div>
                    <div class="absolute inset-x-1 top-0 h-0.5 bg-[#a6925e] opacity-30"></div>
                    <div class="absolute inset-x-1 bottom-0 h-0.5 bg-[#a6925e] opacity-30"></div>

                    {{-- Иконка с двумя человечками --}}
                    <div
                        class="flex items-center justify-center w-7 h-7 rounded-full bg-[#252117] border border-[#514b3c] mr-2 group-hover:border-[#a6925e] transition-all duration-300">
                        <svg class="w-4 h-4 text-[#e5b769]" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="3"></circle>
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                    </div>

                    {{-- Текст --}}
                    <div class="flex flex-col">
                        <span class="text-[10px] text-[#9a9483]">Онлайн:</span>
                        <span
                            class="text-[#7cfc00] text-xs font-medium group-hover:text-[#90ff36] transition-colors duration-300">{{ $onlineCount }}
                            <span class="text-[10px] opacity-60">игроков</span></span>
                    </div>

                    {{-- Анимированная стрелка --}}
                    <div
                        class="ml-auto transform translate-x-1 group-hover:translate-x-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
                        <svg class="h-3.5 w-3.5 text-[#e5b769]" fill="currentColor" viewBox="0 0 24 24">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>
                </a>

                {{-- Карточка времени --}}
                <div
                    class="relative flex items-center px-3 py-2 bg-gradient-to-br from-[#2a2621] to-[#201d18] rounded-md border border-[#514b3c]">
                    {{-- Декоративные элементы --}}
                    <div
                        class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
                    </div>
                    <div
                        class="absolute right-0 top-0 h-full w-1 bg-gradient-to-b from-[#a6925e] to-transparent opacity-50">
                    </div>
                    <div class="absolute inset-x-1 top-0 h-0.5 bg-[#a6925e] opacity-30"></div>
                    <div class="absolute inset-x-1 bottom-0 h-0.5 bg-[#a6925e] opacity-30"></div>

                    {{-- Иконка --}}
                    <div
                        class="flex items-center justify-center w-7 h-7 rounded-full bg-[#252117] border border-[#514b3c] mr-2">
                        <svg class="w-4 h-4 text-[#e5b769]" fill="currentColor" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                    </div>

                    {{-- Текст --}}
                    <div class="flex flex-col">
                        <span class="text-[10px] text-[#9a9483]">Время:</span>
                        <span class="text-[#d3c6a6] text-xs font-medium" id="server-time">14:33</span>
                    </div>
                </div>
            </div>

            {{-- Нижняя панель с кнопкой выхода и телеграм --}}
            <div class="flex items-center justify-between px-3 py-1 max-w-md mx-auto">
                {{-- Телеграм иконка --}}
                <a href="https://t.me/your_game_channel" class="flex items-center group" target="_blank">
                    <div
                        class="flex items-center justify-center w-8 h-8 rounded-full bg-[#252117] border border-[#514b3c] group-hover:border-[#a6925e] group-hover:bg-[#2b271e] transition-all duration-300">
                        <svg class="w-4 h-4 text-[#e5b769] group-hover:text-[#f0d89e]" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path
                                d="M9.78 18.65l.28-4.23 7.68-6.92c.34-.31-.07-.46-.52-.19L7.74 13.3 3.64 12c-.88-.25-.89-.86.2-1.3l15.97-6.16c.73-.33 1.43.18 1.15 1.3l-2.72 12.81c-.19.91-.74 1.13-1.5.71L12.6 16.3l-1.99 1.93c-.23.23-.42.42-.83.42z">
                            </path>
                        </svg>
                    </div>
                </a>

                {{-- Кнопка выхода --}}
                <form action="{{ route('logout') }}" method="POST" class="inline-block">
                    @csrf
                    <button type="submit"
                        class="relative overflow-hidden bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] text-[#d3c6a6] text-xs px-4 py-1.5 rounded-md border border-[#514b3c] hover:border-[#a6925e] hover:text-[#e5b769] transition-all duration-300 hover:shadow-md focus:outline-none group">
                        <span class="relative z-10">Выйти</span>
                        <span
                            class="absolute inset-0 bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-0 group-hover:opacity-20 transform translate-x-full group-hover:translate-x-0 transition-all duration-700"></span>
                    </button>
                </form>
            </div>

            {{-- Декоративный разделитель --}}
            <div class="relative h-1 w-full max-w-xs mx-auto mb-1">
                <div
                    class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-[#a6925e] to-transparent opacity-40">
                </div>
            </div>

            {{-- Копирайт в стиле фэнтези-игры --}}
            <div class="text-center text-[#9a9483] text-[10px] py-1 relative">
                2025
                {{-- Скрытый доступ через название игры --}}
                @if (auth()->check() && auth()->user()->role === 'admin')
                    <a href="{{ route('admin.dashboard') }}"
                        class="inline-block hover:text-[#d3c6a6] transition-colors duration-300">
                        <span class="text-[#d3c6a6] font-medium hover:text-[#e5b769]">Echoes of Eternity</span>
                    </a>
                @else
                    <span class="text-[#d3c6a6] font-medium">Echoes of Eternity</span>
                @endif
            </div>
        </footer>
    </div> {{-- Закрытие основного контейнера --}}

    <script>
        // document.addEventListener('DOMContentLoaded', function() {
        //     const skillForms = document.querySelectorAll('form[action*="use_skill"]');

        //     skillForms.forEach(form => {
        //         form.addEventListener('submit', function(e) {
        //             e.preventDefault();

        //             const formData = new FormData(this);
        //             const url = this.getAttribute('action');

        //             const loadingIndicator = document.createElement('div');
        //             loadingIndicator.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        //             loadingIndicator.innerHTML = '<div class="bg-white p-4 rounded-lg">Применение умения...</div>';
        //             document.body.appendChild(loadingIndicator);

        //             fetch(url, {
        //                 method: 'POST',
        //                 body: formData,
        //                 headers: {
        //                     'X-Requested-With': 'XMLHttpRequest'
        //                 }
        //             })
        //             .then(response => {
        //                 document.body.removeChild(loadingIndicator); // Удаляем индикатор
        //                 window.location.reload();
        //             })
        //             .catch(error => {
        //                 console.error('Ошибка:', error);
        //                 document.body.removeChild(loadingIndicator); // Удаляем даже при ошибке
        //                 window.location.reload();
        //             });
        //         });
        //     });
        // });
    </script>
    <style>
        #progressBar {
            transition: width 0.1s linear;
        }
    </style>
    @vite(['resources/js/attackLimiter.js'])
</body>

</html>